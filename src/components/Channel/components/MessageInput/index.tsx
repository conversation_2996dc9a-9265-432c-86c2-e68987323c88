/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable complexity */
/* eslint-disable @typescript-eslint/restrict-template-expressions */
/* eslint-disable max-lines */
/* eslint-disable max-statements */
/* eslint-disable indent */
import { useState, useRef, useEffect, useMemo } from 'react';
import { getCleanText } from '@/components/CKEditor/utils';
import { useDeepCompareEffect } from 'ahooks';
import { useSendMessage } from '@/hooks/useSendMessage';
import { IMSDK } from '@/layouts/BasicLayout';
import { notification, message as Message } from '@ht/sprite-ui';
import {
  useConversationStore,
  useGlobalModalStore,
  useGroupMembersStore,
  useUserStore,
} from '@/store';
import {
  BotCommandItem,
  ConversationItem,
  MessageItem,
  GroupStatus,
} from '@ht/openim-wasm-client-sdk';
import { getFileType, canSendImageTypeList } from '@/utils/common';
import { useFileMessage } from '@/hooks/useFileMessage';
import { ProsemirrorAdapterProvider } from '@prosemirror-adapter/react';
import { EditorProvider } from '@/components/MdEditor/editorContent';
import { v4 as uuidv4 } from 'uuid';
import _, { debounce, isNil, isEmpty } from 'lodash';
import emitter from '@/utils/events';
import sendIcon from '@/assets/channel/messageInput/send.png';
import stopIcon from '@/assets/channel/messageInput/stop.svg';
import quoteDeleteIcon from '@/assets/channel/messageInput/quoteDelete.svg';
import configSetIcon from '@/assets/channel/robotConversationList/configSet.svg';
import { EditMd } from '@/components/MdEditor';
import { useKeyboardEvent } from '@/hooks/useKeyboardEvent';
import { KeyboardEventSource, charKeyCodes } from '@/utils/constants';
import { useRobot } from '@/hooks/useRobot';
import classNames from 'classnames';
import {
  getStatusImgSrcByStatus,
  getShowDescByStatus,
} from '@/components/UserState/SetStateModal/const';
import useUserInfo from '@/hooks/useUserInfo';
import { shallow } from 'zustand/shallow';
import { RobotCommand } from './RobotCommand';
import QuoteMessageRender from '../MessageItem/QuoteMessageRender';
import FileRender from './FileRender';
import RobotConfigModal from '../MessageListForeword/RobotConfigModal';
import styles from './index.less';
import TextOperate from './TextOperate';

export interface FileListType {
  type: string;
  file?: File;
  id: string;
  url: string;
  docInfo?: any;
  isImage?: boolean;
  isClouDoc?: boolean;
  // Bot对话场景下的文件上传相关字段
  message?: MessageItem; // 创建的消息对象
  s3Path?: string; // 上传后的S3路径
  uploadStatus?: 'uploading' | 'success' | 'failed'; // 上传状态
}

// 判断是不是指令
export const startsWithSlashNoSpace = (str: string) => {
  return /^\/(?!\s)/.test(str);
};

const defaultFormatterDisplayPref =
  localStorage.getItem('linkim.messageInput.formatterDisplayPref') !== 'false';

export interface editMdRefType {
  clearValue: () => void;
  focus: () => void;
  getValue: () => void;
  getMentionList: () => string[];
  getNodeJson: () => void;
  activeMarks: Set<string>;
}

interface robotCommandRefProps {
  manuallySubmit: () => void;
  getFormState: () => any;
}

type MessasgeInputProps = {
  // threadID?: string;
  inRightThread?: boolean;
  conversation: ConversationItem | undefined;
  multiSessionDataIsEmpty?: boolean;
};

type DraftTextProps = {
  value: string;
  replyMsg: string;
  atUserList: string[];
  fileList: FileListType[];
  selectedCommand: BotCommandItem | null;
  formInfo: {
    values: any;
    showMore: boolean;
  };
};
const MessageInput = (props: MessasgeInputProps) => {
  const {
    inRightThread,
    conversation,
    multiSessionDataIsEmpty = false,
  } = props;
  const [value, setValue] = useState('');
  const [replyMsg, setReplyMsg] = useState<MessageItem>();
  const { sendMessage, sendThreadMessage, sendStreamMessage } =
    useSendMessage(conversation);
  const rightAreaInGroupConversation = useConversationStore(
    (state) => state.rightAreaInGroupConversation
  );

  const currentMemberInGroup = useConversationStore(
    (state) => state.currentMemberInGroup
  );
  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );
  const currentMultiSession = useConversationStore(
    (state) => state.currentMultiSession
  );

  const llmLoading = useConversationStore((state) => state.llmLoading);

  const currentGroupInfo = useConversationStore(
    (state) => state.currentGroupInfo
  );
  const updateLlmLoading = useConversationStore(
    (state) => state.updateLlmLoading
  );
  const isMultiSession = useConversationStore((state) => state.isMultiSession);
  const botConfig = useConversationStore((state) => state.botConfig);
  const currentBotConfig = useConversationStore(
    (state) => state.currentBotConfig
  );
  const botConfigModal = useConversationStore((state) => state.botConfigModal);
  const updateBotConfigModal = useConversationStore(
    (state) => state.updateBotConfigModal
  );

  const setCurrentMessageInputValue = useConversationStore(
    (state) => state.setCurrentMessageInputValue
  );

  const isMultiSessionCreate =
    isMultiSession &&
    isEmpty(currentBotConfig) &&
    multiSessionDataIsEmpty &&
    botConfig?.length > 0;
  const showMultiSessionSet =
    isMultiSession &&
    (!multiSessionDataIsEmpty || !isEmpty(currentBotConfig)) &&
    botConfig?.length > 0;

  const [hasInit, setHasInit] = useState(false);
  const [draftText, setDraftText] = useState<DraftTextProps | undefined>(
    undefined
  );

  const { draftText: draftTextStr } = currentConversation || {};
  useEffect(() => {
    try {
      const draftJson = draftTextStr ? JSON.parse(draftTextStr) : null;
      //
      if (!hasInit) {
        if (draftJson != null && draftJson !== '') {
          setDraftText(draftJson);
          setCurrentMessageInputValue(draftJson);
        } else {
          setDraftText(undefined);
          setHasInit(true);
          setCurrentMessageInputValue(null);
        }
      }
    } catch (e) {
      setDraftText(undefined);
      setHasInit(true);
    }
  }, [
    currentConversation,
    draftText,
    draftTextStr,
    hasInit,
    setCurrentMessageInputValue,
  ]);

  const setNetErrorTooltipOpen = useGlobalModalStore(
    (state) => state.setNetErrorTooltipOpen
  );

  const { userState, userDetail } = useUserInfo(currentConversation?.userID);

  // 检查是否为多模态bot对话场景
  const isMultiModalBotConv = useMemo(() => {
    if (currentConversation?.userID && !userDetail) {
      // 数据没返回的时候 默认给true 避免bot切换闪烁
      return true;
    }
    if (!currentConversation?.userID || !userDetail?.ex) {
      return false;
    }
    try {
      const exData = JSON.parse(userDetail?.ex || '{}');
      return !!exData.stream && !!exData.isBot && !!exData.multiModal;
    } catch (e) {
      return false;
    }
  }, [currentConversation?.userID, userDetail?.ex]);

  // 检查是否为bot对话场景
  const isBot = useMemo(() => {
    if (!currentConversation?.userID || !userDetail?.ex) {
      return false;
    }
    try {
      const exData = JSON.parse(userDetail?.ex || '{}');
      return !!exData.isBot;
    } catch (e) {
      return false;
    }
  }, [currentConversation?.userID, userDetail?.ex]);

  const [isComposition, setIsComposition] = useState<boolean>(false);
  const [typing, setTyping] = useState<boolean>(false);
  const [fileList, setFileList] = useState<FileListType[]>([]);
  const [showFormater, setShowFormater] = useState<boolean>(
    !!defaultFormatterDisplayPref
  );
  // const [mentionArr, setMentionArr] = useState<GroupMemberItem[]>([]);
  const { createFileMessage, createFileMessageForBot } = useFileMessage();
  const editMdRef = useRef<editMdRefType>();
  const groupMemberList = useGroupMembersStore(
    (state) => state.groupMemberList
  );

  const messageInputRef = useRef();
  const pasteContainerRef = useRef();
  const { submitRobotCommand } = useRobot(conversation?.groupID as string);

  const { syncState, connectState } = useUserStore(
    (state) => ({
      syncState: state.syncState,
      connectState: state.connectState,
    }),
    shallow
  );

  const showConnecting =
    syncState === 'success' &&
    (connectState === 'loading' || connectState === 'failed');

  const handleEnter = () => {
    if (showCommandList || selectedCommand?.options?.length) {
      return;
    }

    if (!handleCanSend) {
      return;
    }
    const val = editMdRef.current?.getValue() || value;
    enterToSend(val);
  };

  const [placeholderText, setPlaceholderText] = useState<any>('');
  const [showCommandList, setShowCommandList] = useState(false);
  const [selectedCommand, setSelectedCommand] =
    useState<BotCommandItem | null>();

  const [formInfo, setFormInfo] = useState<DraftTextProps['formInfo']>();

  const robotCommandRef = useRef<robotCommandRefProps>();

  const handleKeyDown = useKeyboardEvent(
    KeyboardEventSource.MESSAGE_INPUT,
    {
      onEnter: () => {
        // 如果是ctrl+enter，不处理

        if (!isComposition) {
          handleEnter();
        }
      },
      onEvent: (e) => {
        // 打0-9 a-z时设置正在输入flag,隐藏placeholder
        setTyping(charKeyCodes.includes(e.keyCode));
      },
    },
    {
      shouldHandle: !selectedCommand,
      shouldPreventDefault: true,
    }
  );
  useEffect(() => {
    // 精准匹配才显示指令modal
    if (startsWithSlashNoSpace(value.trim())) {
      setShowCommandList(true);
    } else {
      setShowCommandList(false);
    }
  }, [value]);

  useEffect(() => {
    if (draftText != null) {
      setPlaceholderText('');
      return;
    }
    if (conversation?.groupID) {
      if (currentGroupInfo?.status === GroupStatus.Muted) {
        setPlaceholderText(<>当前群聊已禁言，仅群主及管理员可以发消息</>);
      } else {
        setPlaceholderText(<>{`向 #${conversation?.showName} 发送消息`}</>);
      }
    } else {
      const personStatus = (userState?.code || userState?.desc) && (
        <span className={styles.state}>
          <img src={getStatusImgSrcByStatus(userState)} />
          <span>{getShowDescByStatus(userState)}</span>
        </span>
      );
      setPlaceholderText(
        <>
          给 {conversation?.showName} {personStatus} 发送消息
        </>
      );
    }
  }, [conversation, userState, currentGroupInfo, hasInit, draftText]);

  const grouMemberList = useMemo(() => {
    return conversation?.groupID
      ? [
          {
            nickname: `所有人`,
            userID: 'AtAllTag',
          },
        ].concat(
          groupMemberList.filter(
            (member) => member.userID !== currentMemberInGroup?.userID
          )
        )
      : [];
  }, [conversation?.groupID, currentMemberInGroup?.userID, groupMemberList]);

  useDeepCompareEffect(() => {
    if (llmLoading) {
      updateLlmLoading(false);
    }
    clear();
    emitter.on('REPLAYMSG', replyHandler);
    return () => {
      emitter.off('REPLAYMSG', replyHandler);
    };
  }, [conversation?.conversationID]);

  // useDeepCompareEffect(() => {
  //   getMemberData(true);
  //   return () => {
  //     resetState();
  //   };
  // }, [conversation?.conversationID]);

  const clear = () => {
    editMdRef.current?.clearValue();
    setValue('');
    setReplyMsg(undefined);
    setFileList([]);
  };

  const streamBreak = async () => {
    IMSDK.stopMsgToBot({
      conversationID: conversation?.conversationID,
    })
      .then((res) => {
        if (res?.data) {
          updateLlmLoading(false);
        }
      })
      .catch((err) => {
        console.error('stopMsgToBot', err);
      });
  };

  const enterToSend = async (html: string) => {
    if (html.length > 2000) {
      notification.open({
        message: '消息超长',
        description: '单条消息最长可发送2000字，请拆分后再尝试',
      });
      return;
    }
    if (llmLoading || showCommandList) {
      return;
    }

    if (showConnecting) {
      setNetErrorTooltipOpen(true);
      return;
    }
    if (
      conversation?.groupID &&
      (!currentMemberInGroup || !currentMemberInGroup.userID) // 群聊用户不在组的情况
    ) {
      return;
    }

    const cleanText = getCleanText(html);
    let message;

    if (isBot) {
      // Bot对话场景：发送已上传的文件，再发送流式消息
      if (fileList && fileList.length > 0) {
        await sendUploadedFiles(fileList);
      }
      if (!cleanText && !fileList.length) {
        return;
      }
      message = (await IMSDK.createTextMessage(cleanText)).data;

      const curConversation = isMultiSession
        ? currentMultiSession
        : currentConversation;
      sendStreamMessage({
        recvUser: userDetail,
        curMsg: message,
        curConversation,
      });

      // 如果有文件或有文本内容，清除输入框
      if (cleanText || (fileList && fileList.length > 0)) {
        clear();
        return;
      }
    } else {
      // 非Bot对话场景：保持原有逻辑
      if (cleanText) {
        const atUserList = editMdRef?.current?.getMentionList();
        if (atUserList && atUserList.length > 0) {
          message = await getAtMessage(atUserList, cleanText);
        } else if (replyMsg) {
          // 创建引用消息
          message = (
            await IMSDK.createQuoteMessage({
              text: cleanText,
              message: JSON.stringify(replyMsg),
            })
          ).data;
        } else {
          message = (await IMSDK.createTextMessage(cleanText)).data;
        }

        if (rightAreaInGroupConversation === 'thread') {
          sendThreadMessage(message, inRightThread);
        } else {
          sendMessage({ message });
        }
      }

      // 非Bot对话场景的文件处理
      if (fileList && fileList.length > 0) {
        fileSend(fileList);
      }
    }

    setCurrentMessageInputValue(null);
    setDraftText(undefined);

    setShowCommandList(false);

    setFormInfo(undefined);

    clear();
  };

  const debounceFetch = debounce(enterToSend, 300);

  // @消息
  const getAtMessage = async (atUserList: string[], cleanText: string) => {
    const atUserIDList = [];
    const atUsersInfo = [];
    const idx = atUserList.findIndex((item) => item === 'AtAllTag');
    if (idx > -1) {
      atUserList.splice(idx, 1);
      atUserIDList.push('AtAllTag');
      atUsersInfo.push({
        atUserID: 'AtAllTag',
        groupNickname: '所有人',
      });
    }
    const { data } = await IMSDK.getUsersInfo(atUserList);
    for (const user of data) {
      atUserIDList.push(user.userID);
      atUsersInfo.push({
        atUserID: user.userID,
        groupNickname: user.nickname,
      });
    }
    const params = {
      text: cleanText,
      atUserIDList,
      atUsersInfo,
    };
    if (replyMsg) {
      params.message = replyMsg;
    }
    const message = (await IMSDK.createTextAtMessage(params)).data;
    return message;
  };

  // Bot对话场景：发送已经上传好的文件消息
  const sendUploadedFiles = async (list: FileListType[]) => {
    for (const mesItem of list) {
      if (!mesItem.isClouDoc && mesItem.message) {
        try {
          // 如果文件已经成功上传且有s3Path，直接发送带有s3Path的消息
          if (mesItem.uploadStatus === 'success' && mesItem.s3Path) {
            const messageWithS3Path = {
              ...mesItem.message,
              s3Path: mesItem.s3Path,
              subConversationId: conversation?.subConversationId || '',
            };
            console.log('发送已上传的文件消息:', messageWithS3Path);
            await sendMessage({ message: messageWithS3Path });
          } else if (mesItem.uploadStatus === 'failed') {
            // 如果上传失败，发送原始消息
            console.log('发送上传失败的文件消息:', mesItem.message);
            await sendMessage({ message: mesItem.message });
          } else if (mesItem.uploadStatus === 'uploading') {
            // 如果还在上传中，跳过此文件，避免重复上传
            console.log('文件还在上传中，跳过发送...');
            // 不发送正在上传的文件，等待下次发送时再处理
            continue;
          }
        } catch (error) {
          console.error('发送文件消息失败:', error);
        }
      }
    }
  };

  // bot有文件时是否可以发送
  const handleCanSend = useMemo(() => {
    if (
      (showCommandList || value === '' || getCleanText(value) === '') &&
      fileList.length === 0
    ) {
      if (!llmLoading) {
        return false;
      }
    } else if (isBot) {
      const hasUploadingFiles =
        fileList.filter((i) => i.uploadStatus === 'uploading').length > 0;
      if (hasUploadingFiles) {
        // 有上传中的文件
        return false;
      }
      const hasFailedFiles = fileList.filter(
        (i) => i.uploadStatus === 'failed'
      );
      // 无文本内容 且全是上传失败的文件
      if (
        (showCommandList || value === '' || getCleanText(value) === '') &&
        fileList.length > 0 &&
        hasFailedFiles.length === fileList.length
      ) {
        return false;
      }
    }
    return true;
  }, [fileList, isBot, llmLoading, showCommandList, value]);

  // 重新上传失败的文件
  const retryUploadFile = async (fileId: string) => {
    const fileItem = fileList.find((item) => item.id === fileId);
    if (!fileItem || !fileItem.file) {
      console.error('找不到要重新上传的文件');
      return;
    }

    if (!isMultiModalBotConv) {
      console.error('只有Bot对话场景支持重新上传');
      return;
    }

    // 更新状态为上传中
    setFileList((pre) =>
      pre.map((item) =>
        item.id === fileId
          ? {
              ...item,
              uploadStatus: 'uploading' as const,
            }
          : item
      )
    );

    try {
      // 1. 创建文件消息
      const message = await createFileMessageForBot(fileItem.file);

      // 2. 上传文件获取URL
      const uploadResult = await IMSDK.uploadFileByMessage(message);

      // 3. 更新文件列表，添加消息和s3Path信息
      setFileList((pre) =>
        pre.map((item) =>
          item.id === fileId
            ? {
                ...item,
                message,
                s3Path: uploadResult.data?.url,
                uploadStatus: 'success' as const,
              }
            : item
        )
      );
    } catch (error) {
      console.error('重新上传文件失败:', error);
      // 更新为失败状态
      setFileList((pre) =>
        pre.map((item) =>
          item.id === fileId
            ? {
                ...item,
                uploadStatus: 'failed' as const,
              }
            : item
        )
      );
    }
  };

  // Bot对话场景的文件发送：先上传文件获取URL，再发送文件消息（保留作为备用）
  const fileSendForBot = async (list: FileListType[]) => {
    for (const mesItem of list) {
      if (!mesItem.isClouDoc && mesItem.file) {
        try {
          // 1. 创建文件消息（使用专门的Bot方法）
          const message = await createFileMessageForBot(mesItem.file);

          // 2. 上传文件获取URL
          const uploadResult = await IMSDK.uploadFileByMessage(message);
          console.log('uploadResult', uploadResult);

          // 3. 将URL放入消息的s3Path字段
          const messageWithS3Path = {
            ...message,
            s3Path: uploadResult.data?.url,
            subConversationId: conversation?.subConversationId || '',
          };
          console.log('messageWithS3Path', messageWithS3Path);

          // 4. 发送文件消息
          await sendMessage({
            message: messageWithS3Path,
          });
        } catch (error) {
          console.error('Bot文件上传失败:', error);
          // 如果上传失败，仍然发送原始文件消息
          const message = await createFileMessageForBot(mesItem.file);
          await sendMessage({ message });
        }
      }
    }
  };

  // 文件/图片/云文档（非Bot对话场景）
  const fileSend = async (list: FileListType[]) => {
    for (const mesItem of list) {
      if (mesItem.isClouDoc) {
        const { data } = await IMSDK.createCustomMessage({
          data: JSON.stringify({
            type: 'clouddocument',
            content: {
              ...mesItem.docInfo,
            },
          }),
          extension: '',
          description: '',
        });
        sendMessage({
          message: data,
        });
      } else {
        const data = await createFileMessage(mesItem?.file);
        sendMessage({
          message: data,
        });
      }
    }
  };

  const handleCompositionStart = () => {
    setIsComposition(true);
  };

  const handleCompositionEnd = () => {
    setIsComposition(false);
  };

  const replyHandler = (msg: MessageItem) => {
    if (
      (conversation && !conversation?.groupID && !msg.groupID) ||
      (msg.groupID && msg.groupID === conversation?.groupID)
    ) {
      setReplyMsg(msg);
      if (msg) {
        setShowCommandList(false);
        setSelectedCommand(null);
        setFormInfo(undefined);
      }
      editMdRef.current?.focus();
    }
  };

  // 缓存下，避免头像img不停的闪烁
  const ReplyPrefix = useMemo(() => {
    return replyMsg ? (
      <div className={styles.replyBox}>
        <div>
          <QuoteMessageRender message={replyMsg} isInput={true} />
        </div>
        <div className={styles.removeReplyMsg}>
          <img src={quoteDeleteIcon} onClick={() => setReplyMsg(undefined)} />
        </div>
      </div>
    ) : null;
  }, [replyMsg]);

  // 获取多模态Bot配置
  const getBotConfig = () => {
    if (!userDetail?.ex) {
      return null;
    }
    try {
      const exData = JSON.parse(userDetail?.ex || '{}');
      return {
        maxFileSize: parseInt(exData.maxFileSize || '10240', 10),
        maxFileNum: exData.maxFileNum || 10,
        supportedFileTypes: exData.supportedFileTypes || [],
      };
    } catch (e) {
      return null;
    }
  };

  const handleMultiModalUpload = (uploadData: any, type: string) => {
    const botExConfig = getBotConfig();
    const { file } = uploadData;

    // 检查文件数量限制
    if (botExConfig && fileList.length + 1 > botExConfig.maxFileNum) {
      Message.info(`最多只能上传${botExConfig.maxFileNum}个文件`);
      return;
    }

    if (file.size === 0) {
      const text = `${file.name}是一个空文件，不能发送`;
      Message.info(text);
      return;
    }

    // 检查文件大小限制
    const maxSize = botExConfig
      ? botExConfig.maxFileSize * 1024
      : 50 * 1024 * 1024;
    if (file.size > maxSize) {
      Message.info(
        `上传失败，仅可发送${
          botExConfig ? botExConfig.maxFileSize / 1024 : 50
        }M以下的文件`
      );
      return;
    }

    // 检查文件类型限制
    if (
      botExConfig?.supportedFileTypes &&
      botExConfig.supportedFileTypes.length > 0
    ) {
      const fileExt = file.name.split('.').pop()?.toLowerCase();
      if (!fileExt || !botExConfig.supportedFileTypes.includes(fileExt)) {
        Message.info(`暂不支持上传此类文件格式：${fileExt}`);
        return;
      }
    }

    const isImage = canSendImageTypeList.includes(getFileType(file.name));
    let fileUrl = '';
    if (isImage) {
      if (window.electronAPI) {
        fileUrl = file?.previewUrl || '';
      } else {
        fileUrl = URL.createObjectURL(file);
      }
    }

    const fileId = uuidv4();
    const baseFileItem: FileListType = {
      type,
      file,
      id: fileId,
      url: fileUrl,
      isImage,
    };

    // 先添加文件到列表，显示上传中状态
    setFileList((pre) => [
      ...pre,
      {
        ...baseFileItem,
        uploadStatus: 'uploading',
      },
    ]);

    // 异步上传文件，避免阻塞UI
    (async () => {
      try {
        // 1. 创建文件消息
        const message = await createFileMessageForBot(file);

        // 2. 上传文件获取URL
        const uploadResult = await IMSDK.uploadFileByMessage(message);

        // 3. 更新文件列表，添加消息和s3Path信息
        setFileList((pre) =>
          pre.map((item) =>
            item.id === fileId
              ? {
                  ...item,
                  message,
                  s3Path: uploadResult.data?.url,
                  uploadStatus: 'success' as const,
                }
              : item
          )
        );
      } catch (error) {
        console.error('Bot文件上传失败:', error);
        // 更新为失败状态
        setFileList((pre) =>
          pre.map((item) =>
            item.id === fileId
              ? {
                  ...item,
                  uploadStatus: 'failed' as const,
                }
              : item
          )
        );
      }
    })();
    editMdRef.current?.focus();
  };

  const uploadMsg = async (uploadData: any, type: string, path: any) => {
    if (isEmpty(path)) {
      // 处理多文件上传的情况（多模态Bot场景下）
      if (isMultiModalBotConv) {
        handleMultiModalUpload(uploadData, type);
        return;
      }

      const fileEl = uploadData.file as File;
      if (fileEl.size === 0) {
        const text = `${fileEl.name}是一个空文件，不能发送`;
        Message.info(text);
        return;
      }
      if (fileEl.size > 50 * 1024 * 1024) {
        Message.info('仅可发送50M以下的文件');
        return;
      }
      const isImage = canSendImageTypeList.includes(getFileType(fileEl.name));
      let fileUrl = '';
      if (isImage) {
        if (window.electronAPI) {
          fileUrl = fileEl?.previewUrl || '';
        } else {
          fileUrl = URL.createObjectURL(fileEl);
        }
      }
      console.debug({ fileEl });

      setFileList((pre) => {
        return [
          ...pre,
          {
            type,
            file: fileEl,
            id: uuidv4(),
            url: fileUrl,
            isImage,
          },
        ];
      });
    } else {
      setFileList((pre) => {
        return [
          ...pre,
          {
            type: 'file',
            file: {
              name: 'image.png',
              type: 'image/png',
              lastModified: Date.now(),
              path,
            },
            id: uuidv4(),
            url: URL.createObjectURL(uploadData),
            isImage: true,
          },
        ];
      });
    }
    editMdRef.current?.focus();
  };

  const paste = (e: any) => {
    // if (!currentMemberInGroup || !currentMemberInGroup.userID) {
    //   return;
    // }
    const { items } = e.clipboardData || e.originalEvent.clipboardData;
    for (const file of items) {
      const fileType = file.type;
      if (fileType.includes('text')) {
      } else {
        const fileEl = file.getAsFile();
        if (fileEl.size === 0) {
          return;
        }

        const isImage = canSendImageTypeList.includes(getFileType(fileEl.name));
        setFileList((pre) => {
          return [
            ...pre,
            {
              type: 'file',
              file: fileEl,
              id: uuidv4(),
              url: isImage ? URL.createObjectURL(fileEl) : '',
              isImage,
            },
          ];
        });
      }
    }
  };

  const botSubmit = async (values: any, sessionType: number) => {
    const { command, ...data } = values;
    // // 将data中的数据整合为duration: 1days winners: 1 prize: 200 格式，拼接在message中,没值的就不加了
    // const message = `/${command.name} ${Object.entries(data)
    //   .filter(
    //     ([_, value]) => value !== undefined && value !== null && value !== ''
    //   )
    //   .map(([key, value]) => `${key}: ${value}`)
    //   .join(' ')}`;

    let message = `/${command.name}`;
    if (!isEmpty(data)) {
      const options: any[] = command.options || [];
      const obj: any = {};
      // eslint-disable-next-line array-callback-return
      Object.keys(data).map((key) => {
        const item = options.filter((val: any) => val.key === key);
        const label = item[0].name;
        obj[label] = data[key];
      });
      message = `/${command.name} ${JSON.stringify(obj)}`;
    }
    // 先发消息，消息成功后再发机器人消息
    try {
      await enterToSend(`${message}`);
      // 发送消息成功后再调用机器人交互
      await submitRobotCommand({
        userID: currentMemberInGroup?.userID,
        groupID: conversation?.groupID,
        botID: command.botID,
        actionID: command.key,
        actionName: command.name,
        type: 1,
        sessionType,
        data,
      });
    } catch (error) {
      console.error('发送机器人指令失败:', error);
    }
  };

  const setTypingStatus = async (status: boolean) => {
    try {
      if (conversation?.conversationID != null) {
        await IMSDK.changeInputStates({
          conversationID: conversation?.conversationID,
          focus: status,
        });
      }
    } catch (e) {
      console.error('更新输入状态失败', e);
    }
  };

  const cloudUpload = async (docInfo: any) => {
    setFileList((pre) => {
      return [
        ...pre,
        {
          type: 'cloudDoc',
          id: uuidv4(),
          url: '',
          docInfo,
          isClouDoc: true,
        },
      ];
    });
    editMdRef.current?.focus();
  };

  // 监听变化，上报草稿数据
  useEffect(() => {
    try {
      if (editMdRef?.current != null && hasInit) {
        const atUserList = editMdRef?.current?.getMentionList();

        const draftFileList = fileList?.filter(
          (item) => !item.isImage && item.type !== 'file'
        );

        if (
          value === '' &&
          isEmpty(atUserList) &&
          isNil(replyMsg) &&
          isEmpty(draftFileList) &&
          isNil(selectedCommand) &&
          isNil(formInfo)
        ) {
          setCurrentMessageInputValue(null);
          return;
        }

        setCurrentMessageInputValue({
          value: value === '' ? '' : editMdRef?.current?.getNodeJson(),
          atUserList,
          replyMsg,
          fileList: draftFileList,
          selectedCommand,
          formInfo,
        });
      }
    } catch (e) {
      // 尚未init成功
    }
  }, [
    value,
    replyMsg,
    fileList,
    hasInit,
    setCurrentMessageInputValue,
    currentConversation,
    selectedCommand,
    formInfo,
  ]);

  // 根据草稿恢复内容
  useEffect(() => {
    if (hasInit) {
    } else if (draftText == null) {
    } else {
      try {
        const {
          replyMsg: draftReplyMsg,
          fileList: draftFileList,
          selectedCommand: draftSelectedCommand,
          formInfo: draftFormInfo,
        } = draftText;
        // 0、MD编辑器的文本用default恢复
        // 1、过滤图片和本地文件，只保留云文档

        if (draftFileList != null && draftFileList?.length !== 0) {
          const toDraftFileList = draftFileList?.filter(
            (item: FileListType) => !item.isImage && item.type !== 'file'
          );

          setFileList([...toDraftFileList]);
        }
        // 2、恢复引用消息
        if (draftReplyMsg != null && draftReplyMsg !== '') {
          setReplyMsg(draftReplyMsg as unknown as MessageItem);
        }

        // 3、恢复指令消息
        if (draftSelectedCommand != null) {
          setShowCommandList(false);
          setSelectedCommand(draftSelectedCommand);

          // 4、恢复指令表单内容
          if (draftFormInfo != null) {
            setFormInfo(draftFormInfo);
          }
        }
      } catch (e) {}

      setHasInit(true);
    }
  }, [draftText, formInfo, hasInit]);

  const handleTopNumber = () => {
    if (!isBot || isMultiModalBotConv) {
      return '45px';
    }
    return '5px';
  };

  return (
    <div
      className={styles.content}
      style={
        isMultiSessionCreate
          ? { display: 'none' }
          : {
              minHeight: isMultiModalBotConv ? '120px' : '80px',
            }
      }
      ref={messageInputRef}
    >
      {showMultiSessionSet && (
        <div className={styles.robotConfigWarp}>
          <div
            className={styles.robotConfig}
            onClick={() => updateBotConfigModal(true)}
          >
            <img src={configSetIcon} />
            <span>修改对话设置</span>
          </div>
        </div>
      )}
      <EditorProvider>
        <>
          <div
            className={styles.textWarp}
            onPaste={paste}
            onKeyDown={handleKeyDown}
            ref={pasteContainerRef}
            id="pasteContainerId"
            style={selectedCommand ? { display: 'none' } : {}}
          >
            {!typing && value === '' && !selectedCommand && (
              <div
                style={{
                  top: handleTopNumber(),
                }}
                className={styles.placeholder}
              >
                {placeholderText}
              </div>
            )}
            {
              <ProsemirrorAdapterProvider>
                {hasInit && (
                  <EditMd
                    defaultValue={draftText?.value || draftText}
                    onFocus={() => setTypingStatus(true)}
                    onBlur={() => setTypingStatus(false)}
                    groupMemberList={grouMemberList}
                    ref={editMdRef}
                    onChange={(val: string) => {
                      setValue(val);
                    }}
                    onCompositionStart={handleCompositionStart}
                    onCompositionEnd={handleCompositionEnd}
                    showFormater={showFormater}
                    setShowFormater={setShowFormater}
                    uploadMsg={uploadMsg}
                    conversationID={conversation?.conversationID}
                    cloudUpload={cloudUpload}
                    isGroup={!!conversation?.groupID}
                    isMultiModalBotConv={isMultiModalBotConv}
                    isBot={isBot}
                  />
                )}
              </ProsemirrorAdapterProvider>
            }
          </div>
          <>{ReplyPrefix}</>
          {fileList && fileList.length > 0 && (
            <FileRender
              fileList={fileList}
              setFileList={setFileList}
              retryUploadFile={retryUploadFile}
            />
          )}
          <RobotCommand
            ref={robotCommandRef}
            showFormater={showFormater}
            value={value}
            showCommandList={showCommandList}
            setShowCommandList={setShowCommandList}
            selectedCommand={selectedCommand}
            messageInputRef={messageInputRef}
            groupId={conversation?.groupID}
            botId={conversation?.userID}
            onClose={() => {
              setShowCommandList(false);
              setSelectedCommand(null);

              setFormInfo(undefined);
            }}
            onSelected={(command) => {
              setShowCommandList(false);
              setSelectedCommand(command);
              setReplyMsg(undefined);
            }}
            onSubmit={(values) => {
              botSubmit(values, conversation?.conversationType);
              setShowCommandList(false);
              setSelectedCommand(null);
            }}
            editMdRef={editMdRef}
            setFormInfo={setFormInfo}
            formInfo={formInfo}
          />
          <div
            className={styles.footer}
            style={selectedCommand ? { display: 'none' } : {}}
          >
            <TextOperate
              showFormater={showFormater}
              disabledBtn={false}
              activeMarks={editMdRef.current?.activeMarks}
            />
            <div
              className={classNames(
                styles.sendBtn,
                handleCanSend ? styles.canSend : styles.cannotSend
              )}
              onClick={() => {
                if (llmLoading) {
                  streamBreak();
                } else if (handleCanSend) {
                  debounceFetch(value);
                }
              }}
            >
              {llmLoading ? (
                <img src={stopIcon} style={{ width: '20px' }} />
              ) : (
                <img style={{ width: '16px' }} src={sendIcon} />
              )}
            </div>
          </div>
        </>
      </EditorProvider>
      {botConfigModal && (
        <RobotConfigModal
          modalOpen={botConfigModal}
          oncancel={() => updateBotConfigModal(false)}
          botConfig={botConfig}
          defaultValue={currentBotConfig?.data || {}}
          showName={currentConversation?.showName || ''}
        />
      )}
    </div>
  );
};
export default MessageInput;
